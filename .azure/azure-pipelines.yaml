trigger:
  batch: true
  branches:
    include:
      - 'develop'
      - 'sit'
      - 'uat'
      - 'feat/az-pipeline'
      - 'main'
pr: none

pool:
  name: ah01_selfhosted
  demands:
    - docker
    - CONNECT_TO_KUBERNETES
    - CONNECT_TO_ACR
    - MEMORY_500M

variables:
  - group: ahss-common
  - name: CONTAINER_REGISTRY_SERVICE_CONNECTION
    value: 'ADO-ACR-SERVICE-CONNECTION'
  - name: PROJECT_NAME
    value: ahss

stages:
  - stage: Unit_Test
    displayName: Unit_Test
    jobs:
      - job: Unit_Test
        displayName: Unit_Test
        steps:
          - task: CmdLine@2
            displayName: task for Unit_Test
            inputs:
              script: |
                echo Unit_Test

  - stage: Scan_SAST_SCA
    displayName: Scan_SAST_SCA
    jobs:
      - template: scan-sast-sca-veracode.yml
        parameters:
          projectName: $(PROJECT_NAME)
          agentMemory: 6G
          jdkVersionOption: 1.17
          projectFolder: ''
          excludeFolderSAST: ''
          excludeFolderSCA: ''

  - stage: 'Build_and_Deploy_Preview'
    displayName: 'Build and Deploy Preview to Azure static web apps'
    dependsOn: []
    jobs:
      - job: Build_and_Deploy_Preview
        displayName: 'Build and Deploy Preview to Azure static web apps'
        pool:
          name: ah01_selfhosted
          demands:
            - docker
            - CONNECT_TO_KUBERNETES
            - CONNECT_TO_ACR
            - MEMORY_500M
        steps:
          - template: ensure-docker-running.yml
          - script: |
              cd $(Agent.BuildDirectory) && sudo rm -rf buildvolume
              mkdir $(Agent.BuildDirectory)/buildvolume
              cd $(System.DefaultWorkingDirectory)
              cp -r * $(Agent.BuildDirectory)/buildvolume
              sudo cp -r .* $(Agent.BuildDirectory)/buildvolume
              cd $(Agent.BuildDirectory)/buildvolume && pwd && ls -al
            displayName: 'Preparing task'
          - script: |
              cd $(Agent.BuildDirectory)/buildvolume
              if [[ "$(Build.SourceBranch)" == "refs/heads/feat/az-pipeline" ]]; then
                sudo cp .env.feat .env
              elif [[ "$(Build.SourceBranch)" == "refs/heads/sit" ]]; then
                sudo cp .env.sit .env
              elif [[ "$(Build.SourceBranch)" == "refs/heads/uat" ]]; then
                sudo cp .env.uat .env
              elif [[ "$(Build.SourceBranch)" == "refs/heads/main" ]]; then
                sudo cp .env.prod .env
              else
                sudo cp .env.dev .env
              fi
              ls -al
              cat .env
            displayName: 'Copy env file'
          - task: AzureStaticWebApp@0
            inputs:
              app_location: '.'
              output_location: 'dist'
              ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/feat/az-pipeline') }}:
                azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN)
                deployment_environment: 'featurePreview'
              ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/develop') }}:
                azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_DEV)
                deployment_environment: 'devPreview'
              ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/sit') }}:
                azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_SIT)
                deployment_environment: 'sitPreview'
              ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/uat') }}:
                azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_UAT)
                deployment_environment: 'uatPreview'
              ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/main') }}:
                azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_PROD)
                deployment_environment: 'prodPreview'
              cwd: $(Agent.BuildDirectory)/buildvolume
            displayName: 'Deploy Preview to Azure static web apps'
  - stage: 'Build_and_Deploy_Production'
    displayName: 'Build and Deploy Production to Azure static web apps'
    dependsOn: 'Build_and_Deploy_Preview'
    jobs:
    - deployment: Build_and_Deploy_Production
      displayName: 'Build and Deploy Production to Azure static web apps'
      environment: 'production'
      pool:
        name: ah01_selfhosted
        demands:
          - docker
          - CONNECT_TO_KUBERNETES
          - CONNECT_TO_ACR
          - MEMORY_500M
      strategy:
        runOnce:
          deploy:
            steps:
              - template: ensure-docker-running.yml
              - script: |
                  cd $(Agent.BuildDirectory) && sudo rm -rf buildvolume
                  mkdir $(Agent.BuildDirectory)/buildvolume
                  cd $(System.DefaultWorkingDirectory)
                  cp -r * $(Agent.BuildDirectory)/buildvolume
                  sudo cp -r .* $(Agent.BuildDirectory)/buildvolume
                  cd $(Agent.BuildDirectory)/buildvolume && pwd && ls -al
                displayName: 'Preparing task'
              - script: |
                  cd $(Agent.BuildDirectory)/buildvolume 
                  if [[ "$(Build.SourceBranch)" == "refs/heads/feat/az-pipeline" ]]; then
                    sudo cp .env.feat .env
                  elif [[ "$(Build.SourceBranch)" == "refs/heads/sit" ]]; then
                    sudo cp .env.sit .env
                  elif [[ "$(Build.SourceBranch)" == "refs/heads/uat" ]]; then
                    sudo cp .env.uat .env
                  elif [[ "$(Build.SourceBranch)" == "refs/heads/main" ]]; then
                    sudo cp .env.prod .env
                  else
                    sudo cp .env.dev .env
                  fi
                  cat .env
                  ls -al
                displayName: 'Copy env file'
              - task: AzureStaticWebApp@0
                inputs:
                  app_location: '.'
                  output_location: 'dist'
                  ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/feat/az-pipeline') }}:
                    azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN)
                    deployment_environment: 'featureProd'
                  ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/develop') }}:
                    azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_DEV)
                    deployment_environment: ''
                  ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/sit') }}:
                    azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_SIT)
                    deployment_environment: ''
                  ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/uat') }}:
                    azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_UAT)
                    deployment_environment: ''
                  ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/main') }}:
                    azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_PROD)
                    deployment_environment: ''
                  cwd: $(Agent.BuildDirectory)/buildvolume
                displayName: 'Deploy Production to Azure static web apps'