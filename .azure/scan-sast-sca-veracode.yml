parameters:
# vvv COMMON BUILD PARAMETERS vvv #
- name: 'projectName' # used for the ACR repo project namespace
  type: string
- name: 'poolName'
  default: ah01_selfhosted
  type: string
- name: 'vmImage'
  default: '$(BUILD_VM_IMAGE)'
  type: string
- name: 'agentMemory'
  displayName: 'Option to determine which agent to build on in terms of how much memory this build needs'
  default: 500M
  type: string
  values:
  - 500M
  - 1G
  - 2G
  - 6G
- name: 'jdkVersionOption'
  default: '1.11'
  type: string
- name: 'projectFolder'
  type: string
  default: 'ahi-systems-of-record'
- name: 'includeFolderSAST'
  type: string
  default: '*'
- name: 'includeFolderSCA'
  type: string
  default: '*'
- name: 'excludeFolderSAST'
  type: string
  default: '.git'
- name: 'excludeFolderSCA'
  type: string
  default: '/tmp/temp'

jobs:
  - job: Scan_SAST_SCA_Veracode
    displayName: Scan_SAST_SCA_Veracode
    timeoutInMinutes: 90
    pool:
      name: ${{ parameters.poolName }}
      vmImage: ${{ parameters.vmImage }}
      demands:
      - CONNECT_TO_ACR
      - MEMORY_${{ parameters.agentMemory }}
      - ${{ if contains(parameters.jdkVersionOption, '.') }}:
        - JAVA_HOME_${{ split(parameters.jdkVersionOption, '.')[1] }}_X64 # chop off 1.
      - ${{ else }}:
        - JAVA_HOME_${{ parameters.jdkVersionOption }}_X64
    variables:
    - group: ahss-common
    steps:
      - script: |
          sudo systemctl status docker
          sudo systemctl start docker
          docker system df
          sudo docker image prune -a -f
          docker system df
          sudo docker buildx prune -f
          docker system df
        displayName: Cleaning Disk
      - script: |
          cd $(Agent.BuildDirectory) && sudo rm -rf civolume; sudo rm -rf Result; sudo rm -rf project
          mkdir $(Agent.BuildDirectory)/civolume
          cd $(System.DefaultWorkingDirectory)
          cp -r * $(Agent.BuildDirectory)/civolume
          sudo cp -r .* $(Agent.BuildDirectory)/civolume
          cd $(Agent.BuildDirectory)/civolume && pwd && ls -al
          mkdir $(Agent.BuildDirectory)/project
          cd $(System.DefaultWorkingDirectory) && cp -r ${{ parameters.includeFolderSAST }} $(Agent.BuildDirectory)/project/
          cd $(Agent.BuildDirectory)/project/ && rm -rf ${{ parameters.excludeFolderSAST }}
          ls -al $(Agent.BuildDirectory)/project
          printenv | grep -i java
        displayName: 'Preparing task'
      - template: login-to-acr.yml
      - task: CmdLine@2
        displayName: 'Pull baseci image'
        inputs:
          script: |
            docker pull acrah01seadahss01.azurecr.io/ci-image-base/veracode-sast-prisma:latest
            docker pull acrah01seadahss01.azurecr.io/ci-image-base/veracode-sast-upload-pdf-sca-java:latest
      - task: ArchiveFiles@2
        displayName: 'Archive files into a zip'
        inputs:
          rootFolderOrFile: $(Agent.BuildDirectory)/project
          includeRootFolder: false
          archiveType: 'zip'
          archiveFile: '$(Agent.BuildDirectory)/project/$(Build.BuildId).zip'
          replaceExistingArchive: true
      - task: CmdLine@2
        displayName: Veracode - SAST - Scan
        inputs:
          script: |
            docker run --rm \
            -v "$(pwd):/civolume" \
            -v "$(Agent.BuildDirectory)/project/:/veracodevolume" \
            -v "$(Agent.BuildDirectory):/tmp/" \
            -e VERACODE_API_KEY_ID='$(VERACODE_API_KEY_ID)' \
            -e VERACODE_API_KEY_SECRET='$(VERACODE_API_KEY_SECRET)' \
            acrah01seadahss01.azurecr.io/ci-image-base/veracode-sast-prisma:latest \
            /bin/sh -c "cd /security_tool && \
            du -sh /veracodevolume/*.zip; \
            ./veracode static scan /veracodevolume/*.zip \
            --app-id 'AIAAH - Amplify Healthcare Platform' -v \
            --results-file /tmp/SAST_results.json \
            --summary-output /tmp/SAST_humanReadable_results.txt; \
            chmod a+rwx /tmp/*"
            sudo rm -rf /tmp/project
            exit 0
      - task: CmdLine@2
        displayName: Veracode - SAST - print output
        inputs:
          script: |
            cat $(Agent.BuildDirectory)/SAST_results.json
            cat $(Agent.BuildDirectory)/SAST_humanReadable_results.txt
            exit 0
      - task: CmdLine@2
        displayName: Veracode - SCA - Scan
        inputs:
          script: |
            docker run --rm \
            -v "$(Agent.BuildDirectory)/civolume:/civolume" \
            -v "$(Agent.BuildDirectory):/tmp/sca" \
            -v "$JAVA_HOME:/java_home" \
            acrah01seadahss01.azurecr.io/ci-image-base/veracode-sast-upload-pdf-sca-java:latest \
            /bin/sh -c "export SYSTEM_ACCESSTOKEN=$(System.AccessToken) && echo $SYSTEM_ACCESSTOKEN && java --version && cd /civolume/ && touch /tmp/temp && rm -rf ${{ parameters.excludeFolderSCA }} && chmod -R +x ./* \
            && ls -al && cd /srcclr/bin && bash -x ./srcclr scan /civolume/ --allow-dirty --debug 2>&1 | tee -a /tmp/SCA_output_raw.txt \
            && awk '/Summary Report/ {found=1} found' /tmp/SCA_output_raw.txt > /tmp/sca/SCA_output.txt; \
            chmod a+rwx /tmp/sca/*; rm -rf /civolume/*"
            exit 0
      - task: CmdLine@2
        displayName: Sumary result to pulish artifact
        inputs:
          script: |
            cd $(Agent.BuildDirectory)
            sudo rm -rf Result; sudo mkdir Result && sudo mv S*.json S*.txt Result
            ls -al Result
            exit 0
      - task: PublishBuildArtifacts@1
        displayName: Publish scan result to artifact
        inputs:
          PathtoPublish: $(Agent.BuildDirectory)/Result
          ArtifactName: 'Security_Scan_Result'
          publishLocation: 'Container'
      - task: CmdLine@2
        displayName: Clean result
        inputs:
          script: |
            cd $(Agent.BuildDirectory) && sudo rm -rf Result project civolume
