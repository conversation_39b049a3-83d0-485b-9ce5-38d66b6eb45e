# Amplify Health Product Portal

| Light                                                   | Dark                                                  |
| ------------------------------------------------------- | ----------------------------------------------------- |
| ![ahpp-light](./documentation/resources/ahpp-light.png) | ![ahpp-dark](./documentation/resources/ahpp-dark.png) |

## Available Scripts

In the project directory, you can run:

### `npm run dev`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm run build`

Builds the app for production to the `dist` folder.
