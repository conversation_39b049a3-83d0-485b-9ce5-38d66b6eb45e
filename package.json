{"name": "ahpp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build && node --loader ts-node/esm swa-config.ts", "build:prod": "tsc -b && vite build --mode prod && node --loader ts-node/esm swa-config.ts", "build:sit": "tsc -b && vite build --mode sit && node --loader ts-node/esm swa-config.ts", "build:uat": "tsc -b && vite build --mode uat && node --loader ts-node/esm swa-config.ts", "build:dev": "tsc -b && vite build --mode dev && node --loader ts-node/esm swa-config.ts", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:coverage": "jest --coverage", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@okta/okta-auth-js": "^7.8.1", "@okta/okta-react": "^6.9.0", "antd": "^5.20.3", "axios": "^1.7.5", "jest-environment-jsdom": "^29.7.0", "jwt-decode": "^4.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.1", "styled-components": "^6.1.12", "web-vitals": "^2.1.4", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tanstack/react-query": "^5.52.2", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@types/jest": "^29.5.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "husky": "^9.1.6", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-styled-components": "^7.2.0", "jest-transform-stub": "^2.0.0", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.6.2", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write"]}}