import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const configContent: string = `{
  "navigationFallback": {
    "rewrite": "/index.html",
    "exclude": ["/images/*.{png,jpg,gif}", "/css/*"]
  }
}`;

const outputPath: string = path.join(__dirname, 'dist', 'staticwebapp.config.json');

async function createStaticWebAppConfig(): Promise<void> {
    try {
        await fs.mkdir(path.join(__dirname, 'dist'), { recursive: true });
        await fs.writeFile(outputPath, configContent);
        console.log('staticwebapp.config.json has been created in the dist folder.');
    } catch (error) {
        console.error('Error writing staticwebapp.config.json file:', error);
    }
}

createStaticWebAppConfig();
