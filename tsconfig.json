{"files": [], "allowJs": true, "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "compilerOptions": {"target": "ESNext", "module": "NodeNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "NodeNext", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "outDir": "./dist", "declaration": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "swa-config.ts"], "exclude": ["node_modules", "dist"]}