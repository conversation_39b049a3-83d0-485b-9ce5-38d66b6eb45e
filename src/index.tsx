import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import App from './App';
import { ConfigProvider } from 'antd';
import { ThemeProvider, useTheme } from './context/ThemeContext'; // Import the context
import './index.css';

const AppWrapper: React.FC = () => {
    const { isDarkMode } = useTheme();

    return (
        <ConfigProvider
            theme={{
                token: {
                    colorPrimary: '#E7336D',
                    colorBgBase: isDarkMode ? '#292b2c' : '#ffffff',
                    colorText: isDarkMode ? '#ffffff' : '#000000',
                    colorBorder: '#ffffff',
                    colorTextPlaceholder: isDarkMode ? '#ffffff' : '#000000',
                    // Add more tokens here as needed for dark/light mode
                },
                components: {
                    Menu: {
                        colorText: '#ffffff',
                        colorBgTextHover: '#ac4a4a',
                        colorItemBgSelected: 'rgba(231, 51, 109, 0.8)',
                        colorItemTextSelected: '#ffffff',
                        colorItemBg: '#8152a0',
                    },
                    Switch: {
                        handleBg: '#c38ce7',
                    },
                },
            }}
        >
            <Router>
                <Routes>
                    <Route path="/" element={<App />} />
                    {/* Add more routes here */}
                </Routes>
            </Router>
        </ConfigProvider>
    );
};

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
    <React.StrictMode>
        <ThemeProvider>
            <AppWrapper />
        </ThemeProvider>
    </React.StrictMode>,
);
