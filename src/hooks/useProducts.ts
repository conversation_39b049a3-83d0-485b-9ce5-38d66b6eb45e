import { useMutation, useQuery } from '@tanstack/react-query';
import { fetchProducts } from '../api/services/productService';
import { Product } from '../models/product';
import { getRedirectUrl } from '../api/services/uamService.js';
import { useEffect } from 'react';

interface UseProductsProps {
    isAuthenticated: boolean;
    setLoading?: (loading: boolean) => void;
    setLoadingText?: (loadingText: string) => void;
}

export const useProducts = ({ isAuthenticated, setLoading, setLoadingText }: UseProductsProps) => {
    const {
        data: products,
        isLoading,
        isError,
    } = useQuery({
        queryKey: ['fetchProducts'],
        enabled: isAuthenticated,
        queryFn: fetchProducts,
        retry: 1,
    });

    const getRedirectUrlMutation = useMutation({
        mutationFn: ({ appId, redirectUrl }: { appId: string; redirectUrl: string | null }) =>
            getRedirectUrl(appId, redirectUrl),
        onMutate: () => {
            if (setLoading) setLoading(true);
            if (setLoadingText) setLoadingText('Opening product, please wait...');
        },
        onSuccess: async (url) => {
            if (url) window.open(url, '_blank');
            if (setLoading) setLoading(false);
        },
        onError: (error) => {
            console.error('Error getting redirect url: ', error);
            if (setLoadingText) setLoadingText('Failed to open product');
            if (setLoading) {
                setTimeout(() => setLoading(false), 2000);
            }
        },
    });

    const handleProductClick = (product: Product) => {
        if (product.productCode) {
            getRedirectUrlMutation.mutate({ appId: product.productCode, redirectUrl: null });
        } else {
            console.error('Product URL  not found');
        }
    };

    useEffect(() => {
        if (isLoading) {
            if (setLoading) setLoading(true);
            if (setLoadingText) setLoadingText('Loading products...');
        } else if (isError) {
            if (setLoadingText) setLoadingText('Failed to load products');
            if (setLoading) {
                setTimeout(() => setLoading(false), 2000);
            }
        } else if (!isLoading && !isError && setLoading) {
            setLoading(false);
        }
    }, [isLoading, isError, setLoading, setLoadingText]);

    return { products, isLoading, handleProductClick };
};
