import { useEffect, useState } from 'react';
import { jwtDecode } from 'jwt-decode';

interface DecodedToken {
    name?: string;
    email?: string;
    Email?: string;
    iat?: number;
    auth_time?: number;
    memberOf?: string[];
}

export const useUserInfo = () => {
    const [userInfo, setUserInfo] = useState<DecodedToken>({});
    const [lastSeen, setLastSeen] = useState<string>('');
    const [memberOf, setMemberOf] = useState<string[]>([]);

    useEffect(() => {
        const tokenStorage = sessionStorage.getItem('okta-token-storage');
        if (tokenStorage) {
            const tokenObject = JSON.parse(tokenStorage);
            const accessToken = tokenObject.accessToken?.accessToken;

            if (accessToken) {
                const decodedToken: DecodedToken = jwtDecode(accessToken);
                setUserInfo(decodedToken);
                setMemberOf(decodedToken.memberOf || []);
                const timestamp = decodedToken.auth_time || decodedToken.iat;
                if (timestamp) {
                    const date = new Date(timestamp * 1000);
                    const formattedDate = date.toLocaleString('en-US', {
                        weekday: 'short',
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                    });
                    setLastSeen(formattedDate);
                }
            }
        }
    }, []);

    return { userInfo, lastSeen, memberOf };
};