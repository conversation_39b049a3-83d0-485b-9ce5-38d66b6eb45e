import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import App from './App';
import { ConfigProvider } from 'antd';
import { ThemeProvider, useTheme } from './context/ThemeContext'; // Import the context
import './index.css';
import { AuthProvider } from './context/AuthContext';
import Login from './components/login/Login';
import { Security } from '@okta/okta-react';
import OktaAuth from '@okta/okta-auth-js';
import { config } from './config/Config';
import { OktaConfigProvider, useOktaConfig } from './context/OktaConfigContext.js';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import OktaAuthenticate from './components/auth/OktaAuthenticate.js';
import { ApiProvider } from './api/interceptor.js';
import AuthenticationErrorPage from './components/common/Error/AuthenticationErrorPage.js';
import LogoutRoute from './components/route/LogoutRoute.js';
import CustomLoginCallback from './components/route/CustomLoginCallback.js';

const queryClient = new QueryClient();

export const AppWrapper: React.FC = () => {
    const { isDarkMode } = useTheme();
    const { oktaConfig } = useOktaConfig();
    const [oktaAuth, setOktaAuth] = useState<OktaAuth | null>(null);

    useEffect(() => {
        if (config.useOktaAuth) {
            if (oktaConfig && oktaConfig.clientId && oktaConfig.issuer) {
                setOktaAuth(new OktaAuth(oktaConfig));
                sessionStorage.setItem('oktaConfig', JSON.stringify(oktaConfig));
            } else {
                const storedOktaConfig = sessionStorage.getItem('oktaConfig');
                if (storedOktaConfig) {
                    setOktaAuth(new OktaAuth(JSON.parse(storedOktaConfig)));
                }
            }
        }
    }, [oktaConfig]);

    // Function to restore the original URI after authentication
    const restoreOriginalUri = async (_oktaAuth: OktaAuth, originalUri: string | undefined) => {
        window.location.replace(originalUri || '/');
    };

    return (
        <ConfigProvider
            theme={{
                token: {
                    colorPrimary: isDarkMode ? '#FB74A8' : '#E7336D',
                    colorBgBase: isDarkMode ? '#292b2c' : '#ffffff',
                    colorText: isDarkMode ? '#ffffff' : '#000000',
                    colorBorder: '#ffffff',
                    colorTextPlaceholder: isDarkMode ? '#ffffff' : '#000000',
                    // Add more tokens here as needed for dark/light mode
                },
                components: {
                    Menu: {
                        colorText: isDarkMode ? '#292b2c' : '#ffffff',
                        // colorBgTextHover: isDarkMode ? '#f7f3fd' : '#f2eef5',
                        itemHoverColor: isDarkMode ? '#8d67c0' : '#4E1F6D',
                        // itemSelectedBg: isDarkMode ? '#f7f3fd' : '#f2eef5',
                        itemSelectedColor: isDarkMode ? '#8d67c0' : '#4E1F6D',
                        itemBg: isDarkMode ? '#b388eb' : '#8152a0',
                        popupBg: isDarkMode ? '#b388eb' : '#8152a0',
                        itemHoverBg: isDarkMode ? '#f7f3fd' : '#f2eef5',
                    },
                    Switch: {
                        handleBg: isDarkMode ? '#292b2c' : 'white',
                        colorPrimary: isDarkMode ? '#8d67c0' : '#4E1F6D',
                        colorPrimaryHover: isDarkMode ? '#8d67c0' : '#4E1F6D',
                        colorText: 'white',
                    },
                },
            }}
        >
            <QueryClientProvider client={queryClient}>
                <Router>
                    <Routes>
                        <Route path="/error" element={<AuthenticationErrorPage />} />
                        {/* Conditionally render Security component based on the feature flag*/}
                        {config.useOktaAuth && oktaAuth ? (
                            <Route
                                path="*"
                                element={
                                    <Security
                                        oktaAuth={oktaAuth}
                                        restoreOriginalUri={restoreOriginalUri}
                                    >
                                        <AuthProvider oktaAuth={oktaAuth}>
                                            <ApiProvider>
                                                <Routes>
                                                    {/* Public route for login */}
                                                    <Route path="/login" element={<Login />} />

                                                    {/* OKTA callback route for handling authentication response */}
                                                    <Route
                                                        path="/callback"
                                                        element={<CustomLoginCallback />}
                                                    />

                                                    {/* Private routes that require authentication */}
                                                    <Route path="*" element={<App />} />

                                                    <Route
                                                        path="/sso-auth"
                                                        element={<OktaAuthenticate />}
                                                    />

                                                    <Route
                                                        path="/logout"
                                                        element={<LogoutRoute />}
                                                    />
                                                    {/* Add more routes here */}
                                                </Routes>
                                            </ApiProvider>
                                        </AuthProvider>
                                    </Security>
                                }
                            />
                        ) : (
                            // If OKTA is disabled, render the app without Security component
                            <Route
                                path="*"
                                element={
                                    <AuthProvider oktaAuth={null}>
                                        <ApiProvider>
                                            <Routes>
                                                {/* Public route for login */}
                                                <Route path="/login" element={<Login />} />

                                                {/* No OKTA callback needed, just private routes */}
                                                <Route path="*" element={<App />} />

                                                <Route
                                                    path="/sso-auth"
                                                    element={<OktaAuthenticate />}
                                                />

                                                <Route path="/logout" element={<LogoutRoute />} />
                                                {/* Add more routes here */}
                                            </Routes>
                                        </ApiProvider>
                                    </AuthProvider>
                                }
                            />
                        )}
                    </Routes>
                </Router>
            </QueryClientProvider>
        </ConfigProvider>
    );
};

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
    <React.StrictMode>
        <ThemeProvider>
            <OktaConfigProvider>
                <AppWrapper />
            </OktaConfigProvider>
        </ThemeProvider>
    </React.StrictMode>,
);
