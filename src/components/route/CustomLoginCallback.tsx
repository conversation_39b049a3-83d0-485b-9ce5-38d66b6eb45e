import { useNavigate } from 'react-router-dom';
import React, { useEffect } from 'react';
import { useAuth } from '../../context/AuthContext.js';

const CustomLoginCallback: React.FC = () => {
    const navigate = useNavigate();
    const { oktaAuth } = useAuth();

    useEffect(() => {
        const handleCallback = async () => {
            if (sessionStorage.getItem('callbackHandled') === 'true') {
                console.log('Callback already handled, skipping...');
                return;
            }

            if (!oktaAuth) {
                console.error('OktaAuth is not initialized.');
                navigate('/error', {
                    state: { error: 'OktaAuth is not initialized. Please login again.' },
                });
                return;
            }

            sessionStorage.setItem('callbackHandled', 'true');
            try {
                await oktaAuth.handleRedirect();
            } catch (error) {
                console.error('Error during callback: ', error);
                navigate('/error', {
                    state: { error: (error as Error).message || 'Unknown error occurred.' },
                });
            }
        };

        const isCallbackHandled = sessionStorage.getItem('callbackHandled');
        if (!isCallbackHandled) {
            handleCallback();
        }
    }, [oktaAuth, navigate]);

    return null;
};

export default CustomLoginCallback;
