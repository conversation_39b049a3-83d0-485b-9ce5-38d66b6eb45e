import React, { useEffect } from 'react';
import { useAuth } from '../../context/AuthContext.js';
import LoadingOverlay from '../common/LoadingOverlay/LoadingOverlay.js';
import { useNavigate } from 'react-router-dom';

const LogoutRoute: React.FC = () => {
    const { logout } = useAuth();
    const navigate = useNavigate();

    useEffect(() => {
        const handleLogout = async () => {
            await logout();
            navigate("/login");
        };
        handleLogout();
    }, [logout]);

    return <LoadingOverlay loadingText="Logging you out..." />;
};

export default LogoutRoute;
