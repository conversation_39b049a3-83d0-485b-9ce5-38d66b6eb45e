import React, { useEffect, useRef } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useMutation } from '@tanstack/react-query';
import { validateUser } from '../../api/services/userService';
import { getRedirectUrl } from '../../api/services/uamService';
import LoadingOverlay from '../common/LoadingOverlay/LoadingOverlay';

interface PrivateRouteProps {
    element: React.ReactElement;
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ element }) => {
    const { isAuthenticated } = useAuth();

    const appId = sessionStorage.getItem('appId');
    const redirectUrl = sessionStorage.getItem('redirectUrl');
    const isRedirected = sessionStorage.getItem('isRedirected') === 'true';
    const hasCalledRedirect = sessionStorage.getItem('hasCalledRedirect') === 'true';

    const hasCalledRedirectRef = useRef(false);

    const { mutate: getRedirectUrlMutation, isPending } = useMutation({
        mutationFn: ({ appId, redirectUrl }: { appId: string; redirectUrl: string | null }) =>
            getRedirectUrl(appId, redirectUrl),
        onSuccess: async (url) => {
            if (url && appId && isRedirected) {
                try {
                    console.log('Redirect URL:', url);
                    sessionStorage.removeItem('appId');
                    sessionStorage.removeItem('isRedirected');
                    sessionStorage.removeItem('redirectUrl');
                    sessionStorage.removeItem('hasCalledRedirect');
                    hasCalledRedirectRef.current = false;
                    console.log('Redirecting to: ', url);
                    window.location.href = url;
                } catch (error) {
                    console.error('Error fetching redirect URL: ', error);
                }
            }
        },
        onError: (error) => {
            console.error('Error generating short-lived token: ', error);
        },
    });

    const { mutate: postValidateUser } = useMutation({
        mutationFn: () => validateUser(),
    });

    useEffect(() => {
        if (
            isAuthenticated &&
            appId &&
            isRedirected &&
            !isPending &&
            !hasCalledRedirect &&
            !hasCalledRedirectRef.current
        ) {
            sessionStorage.setItem('hasCalledRedirect', 'true');
            hasCalledRedirectRef.current = true;
            getRedirectUrlMutation({ appId, redirectUrl });
        }
        if (isAuthenticated) {
            postValidateUser();
        }
    }, [isAuthenticated, appId, isRedirected, isPending, hasCalledRedirect]);

    if (!isAuthenticated) {
        return <Navigate to="/login" />;
    }

    if (isPending || (appId && isRedirected)) {
        return <LoadingOverlay loadingText={''} />;
    }

    return isAuthenticated && !appId && !isRedirected ? element : <Navigate to="/login" />;
};

export default PrivateRoute;
