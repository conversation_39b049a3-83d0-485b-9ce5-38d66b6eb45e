import styled from 'styled-components';
import { Layout } from 'antd';

const shouldForwardProp = (prop: string) => !['isDarkMode', 'isVisible'].includes(prop);

export const AppLayout = styled(Layout).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    display: flex;
    flex-direction: row;
    height: 100vh; /* Full height for the layout */
`;

export const ContentLayout = styled(Layout).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean; collapsed: boolean }>`
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
    margin-top: 64px;
`;

export const MainContentWrapper = styled(Layout.Content).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    height: 100vh;
`;
