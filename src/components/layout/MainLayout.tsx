import React, { useState } from 'react';
import Header from '../common/Header/Header';
import Sidebar from '../common/Sidebar/Sidebar';
import { AppLayout, ContentLayout, MainContentWrapper } from './MainLayout.style';
import { useTheme } from '../../context/ThemeContext';
import { Outlet } from 'react-router-dom';
import { LoadingProvider, useLoading } from '../../context/LoadingContext';
import LoadingOverlay from '../common/LoadingOverlay/LoadingOverlay';

interface MainLayoutProps {
    id?: string;
    children?: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({}) => {
    const { isDarkMode } = useTheme();
    const [collapsed, setCollapsed] = useState(true);

    const toggleCollapsed = () => {
        setCollapsed(!collapsed);
    };

    return (
        <LoadingProvider>
            <AppLayout id={'app-layout'} isDarkMode={isDarkMode}>
                <InnerMainLayout
                    collapsed={collapsed}
                    toggleCollapsed={toggleCollapsed}
                    isDarkMode={isDarkMode}
                />
            </AppLayout>
        </LoadingProvider>
    );
};

const InnerMainLayout: React.FC<{
    collapsed: boolean;
    toggleCollapsed: () => void;
    isDarkMode: boolean;
}> = ({ collapsed, toggleCollapsed, isDarkMode }) => {
    const { loading, loadingText } = useLoading();

    return (
        <>
            {loading && <LoadingOverlay loadingText={loadingText} />}
            <Header toggleCollapsed={toggleCollapsed} />
            <Sidebar collapsed={collapsed} />
            <ContentLayout id={'content-layout'} isDarkMode={isDarkMode} collapsed={collapsed}>
                <MainContentWrapper isDarkMode={isDarkMode}>
                    <Outlet />
                </MainContentWrapper>
                {/*<Footer />*/}
            </ContentLayout>
        </>
    );
};

export default MainLayout;
