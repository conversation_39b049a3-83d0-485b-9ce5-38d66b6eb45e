import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext.js';
import { config } from '../../config/Config';

const OktaAuthenticate: React.FC = () => {
    const { isAuthenticated } = useAuth();
    const appId = new URLSearchParams(window.location.search).get('appId');
    const isRedirected = new URLSearchParams(window.location.search).get('isRedirected');
    const redirectUrl = new URLSearchParams(window.location.search).get('redirectUrl');

    const isValidEncodedRedirectUrl = (url: string) => {
        try {
            const decodedUrl = decodeURIComponent(url);
            const absoluteUrl = new URL(decodedUrl);
            const isValidProtocol = absoluteUrl.protocol === 'https:' || absoluteUrl.protocol === 'http:';
            const hasValidHostname = !!absoluteUrl.hostname;
            return isValidProtocol && hasValidHostname;
        } catch (error) {
            return false;
        }
    };

    const saveAppIdIsRedirectedRedirectUrl = (): boolean => {
        if (appId && isRedirected === 'true') {
            sessionStorage.setItem('appId', appId);
            sessionStorage.setItem('isRedirected', isRedirected);

            if (redirectUrl) {
                if (isValidEncodedRedirectUrl(redirectUrl)) {
                    sessionStorage.setItem('redirectUrl', decodeURIComponent(redirectUrl));
                } else {
                    return false;
                }
            }
            return true;
        }
        return false;
    };

    if (config.useOktaAuth) {
        if (saveAppIdIsRedirectedRedirectUrl()) {
            return isAuthenticated ? <Navigate to="/" /> : <Navigate to="/login" />;
        } else {
            return <div>Invalid parameters or redirectUrl...</div>;
        }
    }
};

export default OktaAuthenticate;
