import React, { useRef, useState } from 'react';
import {
    AgreementText,
    Container,
    EmailInputContainer,
    EmailInputTitle,
    ErrorMessage,
    LeftPanel,
    LoadingContainer, LoadingIcon,
    LoadingOverlay, LoadingText,
    LoginContent,
    LogoContainer,
    RightPanel,
    StyledButton,
    StyledInput,
    StyledTitle,
    TitleContainer,
} from './Login.style';
import { Form } from 'antd';
import { ExclamationCircleFilled } from '@ant-design/icons';
import logo from '../../assets/images/ah-logo-color.png';
import logoDark from '../../assets/images/ah-logo-color-dark.png';
import { useTheme } from '../../context/ThemeContext';
import { useMutation } from '@tanstack/react-query';
import { useOktaConfig } from '../../context/OktaConfigContext.js';
import { useAuth } from '../../context/AuthContext.js';
import { config } from '../../config/Config';
import { getOktaAuthConfig } from '../../api/services/uamService.js';

const Login: React.FC = () => {
    const [formErrors, setFormErrors] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const titleRef = useRef<HTMLHeadingElement>(null);
    const { login } = useAuth();
    const { isDarkMode } = useTheme();
    const [form] = Form.useForm();
    const { setOktaConfig } = useOktaConfig();

    const fetchOktaConfig = useMutation({
        mutationFn: getOktaAuthConfig,
        onSuccess: (dynamicOktaConfig) => {
            setOktaConfig(dynamicOktaConfig);
            console.log('Okta config set successfully:', dynamicOktaConfig);
        },
        onError: (error: Error) => {
            setFormErrors(`Error fetching OKTA config: ${error.message}`);
            setLoading(false);
        },
    });

    const onFinish = async (values: any) => {
        const { email } = values;

        const allowedDomains = ['aia.com', 'amplifyhealth.com', 'medicardphils.com'];
        const emailDomain = email.split('@')[1];
        if (!allowedDomains.includes(emailDomain)) {
            setFormErrors('Only emails from aia.com or amplifyhealth.com or medicardphils.com are allowed.');
            return;
        }

        if (!email) {
            setFormErrors('Please input your email.');
            return;
        }

        const errorMessage = validateEmail(email);
        if (errorMessage) {
            setFormErrors(errorMessage);
            return;
        }

        setLoading(true);
        try {
            if (config.useOktaAuth) {
                console.log('Fetching Okta config for email:', email);
                await fetchOktaConfig.mutateAsync(email);
            } else {
                login(email);
                setLoading(false);
            }
        } catch (error) {
            setFormErrors('Error retrieving OKTA config.');
        }
    };

    const onFinishFailed = (errorInfo: any) => {
        const emailError = errorInfo.errorFields.find((error: any) => error.name[0] === 'email');
        setFormErrors(emailError ? emailError.errors[0] : null);
    };

    const validateEmail = (value: string) => {
        if (!value) {
            return 'Please input your email.';
        }
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/.test(value)) {
            return 'Incorrect email. Type the correct email and try again.';
        }
        return null;
    };

    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const email = e.target.value;
        const errorMessage = validateEmail(email);
        setFormErrors(errorMessage);
    };

    return (
        <Container>
            <LeftPanel />
            <RightPanel isDarkMode={isDarkMode}>
                {loading && (
                    <LoadingOverlay>
                        <LoadingContainer>
                            <LoadingIcon />
                            <LoadingText>Logging you in...</LoadingText>
                        </LoadingContainer>
                    </LoadingOverlay>
                )}
                <LoginContent>
                    <Form
                        name="login"
                        layout="vertical"
                        style={{ width: '100%' }}
                        form={form}
                        onFinish={onFinish}
                        onFinishFailed={onFinishFailed}
                    >
                        <TitleContainer>
                            <LogoContainer>
                                <img src={isDarkMode ? logoDark : logo} alt="Amplify Health" />
                            </LogoContainer>
                            <StyledTitle ref={titleRef}>Product Portal</StyledTitle>
                        </TitleContainer>
                        <EmailInputContainer>
                            <EmailInputTitle>Email</EmailInputTitle>
                            <Form.Item style={{ margin: 0, padding: 0 }} name="email">
                                <StyledInput
                                    placeholder="<EMAIL>"
                                    onChange={handleEmailChange}
                                    hasError={!!formErrors}
                                    disabled={fetchOktaConfig.isPending}
                                />
                            </Form.Item>

                            {/* Show error message */}
                            {formErrors && (
                                <ErrorMessage>
                                    <ExclamationCircleFilled style={{ marginRight: '8px' }} />
                                    {formErrors}
                                </ErrorMessage>
                            )}

                            <Form.Item style={{ margin: 0, padding: 0 }}>
                                <StyledButton
                                    type="primary"
                                    htmlType="submit"
                                    block
                                    isDarkMode={isDarkMode}
                                    loading={fetchOktaConfig.isPending}
                                >
                                    Sign in
                                </StyledButton>
                            </Form.Item>

                            <AgreementText isDarkMode={isDarkMode}>
                                By signing in, you agree to our <a href="#">Privacy
                                Policy</a> and{' '}
                                <a href="#">Terms of Service</a>
                            </AgreementText>
                        </EmailInputContainer>
                    </Form>
                </LoginContent>
            </RightPanel>
        </Container>
    );
};

export default Login;
