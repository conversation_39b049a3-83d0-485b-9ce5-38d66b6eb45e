import styled, { css, keyframes } from 'styled-components';
import { Button, Input, Typography } from 'antd';
import loginBackgroundImage from '../../assets/images/login-bg.png';
import gradientDecorateImage from '../../assets/images/linear-gradient.png';

const { Text } = Typography;

const shouldForwardProp = (prop: string) => !['isDarkMode'].includes(prop);

export const Container = styled.div`
    display: flex;
    height: 100vh;
    width: 100vw;
    overflow-x: hidden;
`;

export const LeftPanel = styled.div`
    flex: 1;
    background: url(${loginBackgroundImage}) no-repeat center center;
    background-size: cover;
`;

export const RightPanel = styled.div.withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    flex: 1;
    max-width: 472px;
    width: 100%;
    background-color: ${({ isDarkMode }) => (isDarkMode ? 'black' : 'white')};
    display: flex;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 15px;
        background-size: 100% 15px;
        background: url('${gradientDecorateImage}') no-repeat center top;
    }
`;

export const LoginContent = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    text-align: center;
    max-width: 100%;
    padding-top: 10px;
`;

export const TitleContainer = styled.div`
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    padding: 0 71px;
`;

export const LogoContainer = styled.div`
    width: 70%;
    height: 48px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    img {
        width: 70%;
        height: 100%;
    }
`;

export const EmailInputTitle = styled.label`
    text-align: left;
    margin-left: 2px;
    font-size: 14px;
    font-weight: 400;
`;

export const EmailInputContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 0 71px;
`;

export const StyledTitle = styled.h2`
    font-size: 40px;
    height: 48px;
    font-weight: 700;
    width: calc(100% - 142px);
    margin: 0 0 24px 0;
    text-align: left;
`;

export const StyledInput = styled(Input)<{ hasError: boolean }>`
    &.ant-input {
        width: 100%;
        height: 40px;
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 8px;
        margin: 0 0 16px 0;

        ${({ hasError }) =>
            hasError &&
            css`
                border-color: #db2828;
            `}
        &:hover {
            border-color: ${({ hasError }) => (hasError ? '#db2828' : '#d9d9d9')};
        }

        &:focus {
            border-color: ${({ hasError }) => (hasError ? '#db2828' : '#d9d9d9')};
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &:not(:focus) {
            border-color: ${({ hasError }) => (hasError ? '#db2828' : '#d9d9d9')};
        }
    }
`;

export const StyledButton = styled(Button).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    max-height: 40px;
    height: 40px;
    width: 100%;
    font-size: 16px;
    font-weight: 500;
    color: ${({ isDarkMode }) => (isDarkMode ? 'black' : 'white')} !important;
    box-shadow: none;
`;

export const AgreementText = styled(Text).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    margin-top: 2rem;
    display: block;
    text-align: left;
    font-size: 14px;
    font-weight: 400;

    a {
        color: ${({ isDarkMode }) => (isDarkMode ? '#FB74A8' : '#E7336D')};
    }
`;

export const ErrorMessage = styled(Text)`
    color: red;
    text-align: left;
    width: 100%;
    margin-top: -10px;
    margin-bottom: 12px;
    font-size: 12px;
    font-weight: 400;
`;

export const LoadingOverlay = styled.div`
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(84, 85, 86, 0.8); /* Lớp nền mờ */
    z-index: 10; /* Đảm bảo nó nằm trên form login */
`;

export const LoadingContainer = styled.div`
    background-color: white; 
    border-radius: 16px; 
    padding: 20px; 
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); 
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 199px; 
    height: 199px; 
`;

const rotate = keyframes`
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
`;

export const LoadingIcon = styled.div`
    width: 56px;
    height: 56px;
    border-radius: 50%;
    position: relative;
    z-index: 1;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: conic-gradient(
                from 0deg,
                rgba(129, 82, 160, 1),
                rgba(248, 17, 106, 1),
                rgba(255, 90, 34, 1) 75%,
                rgba(212, 213, 213, 0.8) 75%
        );
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        padding: 6px;
        animation: ${rotate} 1s linear infinite;
    }
    background-color: white;
`;

export const LoadingText = styled.div`
    margin-top: 32px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(41, 43, 44, 1);
`;