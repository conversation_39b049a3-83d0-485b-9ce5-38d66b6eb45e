import styled from 'styled-components';
import { Avatar, Card, Layout, Typography } from 'antd';

const shouldForwardProp = (prop: string) => !['isDarkMode', 'isVisible'].includes(prop);

export const ProfileContainer = styled(Card).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
  width: 315px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  padding: 0;
`;

export const JobTitle = styled(Typography.Text)`
    display: block;
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
    overflow: hidden;
`;

export const UserEmail = styled(Typography.Text) `
  display: block;
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  overflow: hidden;
  margin-bottom: 16px;
`;

export const LastSeen = styled(Typography.Text) `
  display: flex;
  align-items: center;
  word-wrap: break-word;
  white-space: normal;
  margin-bottom: 16px;

  &::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background-color: #43A047;
    border-radius: 50%;
    margin-right: 8px;
  }
`;

export const RoleTitle = styled(Typography.Text) `
  font-weight: 500;
`;

export const ProfileCardWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
`;

export const ProfileAvatar = styled(Avatar).withConfig({
    shouldForwardProp,
})<ProfileButtonProps>`
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: ${({ isDarkMode }) => (isDarkMode ? '#7f8080' : '#a9aaab')} !important;
`;

export const UserName = styled(Typography.Title).withConfig({
    shouldForwardProp
})`
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
`;

export const NameJobTitleWrapper = styled.div`
  width: 100%;
  padding-right: 90px;
`;

interface ProfileButtonProps {
    id?: string;
    isDarkMode: boolean;
}

export const ProfileButton = styled(Avatar).withConfig({
    shouldForwardProp,
})<ProfileButtonProps>`
    background-color: ${({ isDarkMode }) => (isDarkMode ? '#7f8080' : '#a9aaab')};
    color: ${({ isDarkMode }) => (isDarkMode ? '#292b2c' : 'white')};
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 32px;
    height: 32px;
`;

export const ProfileDiv = styled(Layout).withConfig({
    shouldForwardProp,
})<{ isVisible: boolean; isDarkMode: boolean }>`
    position: absolute;
    top: 50px;
    right: 0;
    width: 300px;
    display: ${({ isVisible }) => (isVisible ? 'block' : 'none')};
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    overflow: hidden; /* To ensure the close button does not push content */
`;
