import React from 'react';
import { Typography } from 'antd';
import { CopyOutlined, UserOutlined } from '@ant-design/icons';
import {
    LastSeen,
    NameJobTitleWrapper,
    ProfileAvatar,
    ProfileButton,
    ProfileCardWrapper,
    ProfileContainer,
    RoleTitle,
    UserEmail,
    UserName,
} from './ProfileCard.style';
import { useUserInfo } from '../../../hooks/useUserInfo.js';

const { Title } = Typography;

interface ProfileCardProps {
    children?: React.ReactNode;
    isDarkMode: boolean;
}

const ProfileCard: React.FC<ProfileCardProps> = ({ children, isDarkMode }) => {
    const { userInfo, lastSeen, memberOf } = useUserInfo();

    const handleCopyEmail = () => {
        const emailToCopy = userInfo.email || userInfo.Email || 'Email';
        navigator.clipboard.writeText(emailToCopy);
    };

    return (
        <ProfileContainer isDarkMode={isDarkMode}>
            <Title level={4} style={{ marginBottom: 32, marginTop: 0 }}>
                Your profile
            </Title>
            <ProfileCardWrapper>
                <ProfileAvatar isDarkMode={isDarkMode}>
                    <ProfileButton
                        size={60}
                        id="profile-button"
                        icon={<UserOutlined />}
                        data-testid="profile-button"
                        isDarkMode={isDarkMode}
                    />
                </ProfileAvatar>
                <NameJobTitleWrapper>
                    <UserName level={5}>{userInfo.name || 'User Name'}</UserName>
                    {/*<JobTitle>Solution Architect</JobTitle>*/}
                </NameJobTitleWrapper>
            </ProfileCardWrapper>
            <UserEmail>
                {userInfo.email || userInfo.Email || 'Email'}
                <CopyOutlined style={{ marginLeft: 8 }} onClick={handleCopyEmail} />
            </UserEmail>
            <LastSeen>Last seen {lastSeen}</LastSeen>
            <RoleTitle>User Roles</RoleTitle>
            <ul style={{ paddingLeft: 16, marginBottom: 16, marginTop: 8 }}>
                {memberOf.length > 0 ? (
                    memberOf.map((role, index) => <li key={index}>{role}</li>)
                ) : (
                    <li>No roles assigned</li>
                )}
            </ul>
            {/*<Divider />*/}
            {/*<Button style={{ fontWeight: 500, fontSize: 16 }} icon={<EditOutlined />}>*/}
            {/*    Edit profile*/}
            {/*</Button>*/}
            {children}
        </ProfileContainer>
    );
};

export default ProfileCard;
