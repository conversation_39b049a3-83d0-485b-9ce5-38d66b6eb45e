import React, { useState, useRef, useEffect } from 'react';
import { UserOutlined } from '@ant-design/icons';
import ProfileCard from './ProfileCard';
import { ProfileButton, ProfileDiv } from './ProfileCard.style';
import { useTheme } from '../../../context/ThemeContext';

const ProfileButtonComponent: React.FC = () => {
    const [isVisible, setIsVisible] = useState(false);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const profileDivRef = useRef<HTMLDivElement>(null);

    const { isDarkMode } = useTheme();

    const toggleProfileDiv = () => {
        setIsVisible(!isVisible);
    };

    // const handleClose = () => {
    //     setIsVisible(false);
    // };

    const handleClickOutside = (event: MouseEvent) => {
        if (
            profileDivRef.current &&
            !profileDivRef.current.contains(event.target as Node) &&
            buttonRef.current &&
            !buttonRef.current.contains(event.target as Node)
        ) {
            setIsVisible(false);
        }
    };

    useEffect(() => {
        if (isVisible) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isVisible]);

    return (
        <div style={{ position: 'relative', marginRight: '4px' }}>
            <ProfileButton
                id="profile-button"
                icon={<UserOutlined />}
                ref={buttonRef}
                onClick={toggleProfileDiv}
                data-testid="profile-button"
                isDarkMode={isDarkMode}
            />
            <ProfileDiv
                ref={profileDivRef}
                isVisible={isVisible}
                isDarkMode={isDarkMode}
                id="profile-div"
            >
                <ProfileCard isDarkMode={isDarkMode} />
            </ProfileDiv>
        </div>
    );
};

export default ProfileButtonComponent;
