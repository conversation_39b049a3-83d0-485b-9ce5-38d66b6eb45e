import React from 'react';
import { LoadingContainer, LoadingIcon, LoadingText, Overlay } from './LoadingOverlay.styles';
import { useTheme } from '../../../context/ThemeContext';

interface LoadingOverlayProps {
    loadingText?: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ loadingText = 'Loading...' }) => {
    const { isDarkMode } = useTheme();

    return (
        <Overlay isDarkMode={isDarkMode}>
            <LoadingContainer isDarkMode={isDarkMode}>
                <LoadingIcon isDarkMode={isDarkMode} />
                <LoadingText isDarkMode={isDarkMode}>{loadingText}</LoadingText>
            </LoadingContainer>
        </Overlay>
    );
};

export default LoadingOverlay;
