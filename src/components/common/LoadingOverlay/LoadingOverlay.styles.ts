import styled, { keyframes } from 'styled-components';

const shouldForwardProp = (prop: string) => !['isDarkMode', 'isVisible'].includes(prop);

export const Overlay = styled.div.withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(84, 85, 86, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
`;

export const LoadingContainer = styled.div.withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    background-color: ${({ isDarkMode }) => isDarkMode ? '#292b2c' : '#ffffff'};
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 199px;
    height: 199px;
`;

const rotate = keyframes`
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
`;

export const LoadingIcon = styled.div.withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    width: 56px;
    height: 56px;
    border-radius: 50%;
    position: relative;
    z-index: 1;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: conic-gradient(
                from 0deg,
                rgba(129, 82, 160, 1),
                rgba(248, 17, 106, 1),
                rgba(255, 90, 34, 1) 75%,
                rgba(212, 213, 213, 0.8) 75%
        );
        mask: linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
        mask-composite: exclude;
        padding: 6px;
        animation: ${rotate} 1s linear infinite;
    }

    background-color: ${({ isDarkMode }) => isDarkMode ? '#292b2c' : '#ffffff'};
`;

export const LoadingText = styled.div.withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    margin-top: 32px;
    font-size: 16px;
    font-weight: 400;
    color: ${({ isDarkMode }) => isDarkMode ? '#ffffff' : '#292b2c'};
`;
