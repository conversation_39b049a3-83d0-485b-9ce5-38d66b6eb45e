import React from 'react';
import { Col, Row } from 'antd';
import ProductCard from '../ProductCard/ProductCard';
import { CodeSandboxOutlined } from '@ant-design/icons';
import { ContainerTitle, AppCardsContainer, AppsContainer } from './Dashboard.style';
import { useTheme } from '../../../context/ThemeContext';
import ProductLogo from '../../../assets/images/ah-product-dummy-logo.svg';

const YourSandbox: React.FC = () => {
    const { isDarkMode } = useTheme();
    return (
        <AppsContainer id={'your-sandbox-app-container'}>
            <ContainerTitle isDarkMode={isDarkMode}>Amplify Health Sandbox</ContainerTitle>
            <AppCardsContainer isDarkMode={isDarkMode}>
                <Row gutter={[16, 16]}>
                    <Col span={6}>
                        <ProductCard
                            title="Product"
                            extra={<CodeSandboxOutlined />}
                            logoUrl={ProductLogo}
                            isDarkMode={isDarkMode}
                        >
                            Amplify Health
                        </ProductCard>
                    </Col>
                    <Col span={6}>
                        <ProductCard
                            title="Product"
                            extra={<CodeSandboxOutlined />}
                            logoUrl={ProductLogo}
                            isDarkMode={isDarkMode}
                        >
                            Amplify Health
                        </ProductCard>
                    </Col>
                    <Col span={6}>
                        <ProductCard
                            title="Product"
                            extra={<CodeSandboxOutlined />}
                            logoUrl={ProductLogo}
                            isDarkMode={isDarkMode}
                        >
                            Amplify Health
                        </ProductCard>
                    </Col>
                    <Col span={6}>
                        <ProductCard
                            title="Product"
                            extra={<CodeSandboxOutlined />}
                            logoUrl={ProductLogo}
                            isDarkMode={isDarkMode}
                        >
                            Amplify Health
                        </ProductCard>
                    </Col>
                </Row>
            </AppCardsContainer>
        </AppsContainer>
    );
};

export default YourSandbox;
