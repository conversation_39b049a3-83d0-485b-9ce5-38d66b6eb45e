import styled from 'styled-components';

const shouldForwardProp = (prop: string) => !['isDarkMode', 'isVisible'].includes(prop);

export const GlassmorphismContainer = styled.div.withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    margin-top: -90px;
    margin-left: 30px;
    margin-right: 30px;
    border: 1px solid var(--Glassmorphism-Outline, #eaeaea);
    box-shadow: 0 3px 12px 0 #0000001a;
    padding: 20px;
    border-radius: 10px;
    border-color: #eaeaea;
    z-index: 1;
    backdrop-filter: blur(11px);
    -webkit-backdrop-filter: blur(11px);
`;
