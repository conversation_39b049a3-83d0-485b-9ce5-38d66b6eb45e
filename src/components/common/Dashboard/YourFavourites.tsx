import { StarFilled } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { List, Skeleton } from 'antd';
import React from 'react';
import { fetchFavoriteProducts } from '../../../api/services/productService';
import ProductLogo from '../../../assets/images/ah-product-dummy-logo.svg';
import { useTheme } from '../../../context/ThemeContext';
import ProductCard from '../ProductCard/ProductCard';
import {
    AppCardsContainer,
    AppsContainer,
    ContainerTitle,
    ProductListItem,
} from './Dashboard.style';

const YourFavourites: React.FC = () => {
    //API call to fetch Favorites
    const { data, isLoading } = useQuery({
        queryKey: ['fetchFavoriteProducts'],
        queryFn: fetchFavoriteProducts,
    });
    const { isDarkMode } = useTheme();
    return (
        <AppsContainer id={'your-favourites-apps-container'}>
            <ContainerTitle isDarkMode={isDarkMode}>Your favourites</ContainerTitle>
            <AppCardsContainer isDarkMode={isDarkMode}>
                <List
                    grid={{ gutter: 24, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }}
                    size="large"
                    dataSource={data || []}
                    renderItem={(item) => (
                        <ProductListItem>
                            <Skeleton loading={isLoading}>
                                <ProductCard
                                    title={item.productName || ''}
                                    extra={
                                        <StarFilled
                                            style={item.isFavorite ? { color: '#E7336D' } : {}}
                                        />
                                    }
                                    logoUrl={ProductLogo}
                                    isDarkMode={isDarkMode}
                                >
                                    {item.description}
                                </ProductCard>
                            </Skeleton>
                        </ProductListItem>
                    )}
                />
            </AppCardsContainer>
        </AppsContainer>
    );
};

export default YourFavourites;
