import styled from 'styled-components';
import { Typography } from 'antd';
const { Text } = Typography;

const shouldForwardProp = (prop: string) => !['isDarkMode', 'isVisible'].includes(prop);

export const AppsContainer = styled.div`
    margin-top: 30px;
    margin-left: 30px;
    margin-right: 30px;
    padding: 20px 0 0 0;
    gap: 10px;
    border-radius: 4px 0 0 0;
    height: 100vh;
`;

export const ContainerTitle = styled(Text).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    width: 96px;
    height: 24px;
    top: 20px;
    left: 16px;
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    text-align: left;
`;

export const AppCardsContainer = styled.div.withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    gap: 24px;
    margin-top: 20px;
`;

export const ProductListItem = styled.div`
    padding: 8px;
`;
