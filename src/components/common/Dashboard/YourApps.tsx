import React from 'react';
import { List, Skeleton } from 'antd';
import ProductCard from '../ProductCard/ProductCard';
import { StarFilled } from '@ant-design/icons';
import InsightStudioLogo from '../../../assets/images/insights-studio.svg';
import CoreClaimsLogo from '../../../assets/images/core-claims.svg';
import ProductLogo from '../../../assets/images/ah-product-dummy-logo.svg';
import { GlassmorphismContainer } from './YourApps.style';
import { AppCardsContainer, ContainerTitle, ProductListItem } from './Dashboard.style';
import { useTheme } from '../../../context/ThemeContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateFavoriteProducts } from '../../../api/services/productService';
import { Product } from '../../../models/product';
import { useOktaAuth } from '@okta/okta-react';
import { useProducts } from '../../../hooks/useProducts.js';

interface YourAppsProps {
    setLoading: (loading: boolean) => void;
    setLoadingText: (loadingText: string) => void;
}

const YourApps: React.FC<YourAppsProps> = ({ setLoading, setLoadingText }) => {
    const { isDarkMode } = useTheme();
    const queryClient = useQueryClient();

    const { authState } = useOktaAuth() || {};
    const isAuthenticated = !!(authState?.isAuthenticated && authState.accessToken);

    const { products, isLoading, handleProductClick } = useProducts({
        isAuthenticated,
        setLoading,
        setLoadingText,
    });

    const mutation = useMutation({
        mutationFn: updateFavoriteProducts,
        onSuccess: (data) => {
            queryClient.setQueryData(['fetchProducts'], (oldData: Product[]) => {
                return oldData.map((product) =>
                    product.productId === data.productId ? data : product,
                );
            });
            queryClient.setQueryData(['fetchFavoriteProducts'], (oldData: Product[]) => {
                if (data.isFavorite) {
                    return [...oldData, data];
                } else {
                    return oldData.filter((product) => product.productId !== data.productId);
                }
            });
        },
    });

    //Mocking logo
    const logos: { [key: string]: string } = {
        AHIS: InsightStudioLogo,
        AHCC: CoreClaimsLogo,
        AHCCSB: CoreClaimsLogo,
        DEFAULT: ProductLogo,
    };
    const handleFavorite = (id?: number, isFavorite?: boolean) => {
        if (id) {
            const updatedProduct = {
                productId: id,
                isFavorite: isFavorite == null ? true : isFavorite,
            };
            mutation.mutate(updatedProduct);
        }
    };
    return (
        <GlassmorphismContainer isDarkMode={isDarkMode}>
            <ContainerTitle
                id={'your-apps-title'}
                isDarkMode={isDarkMode}
                style={{ color: '#fff', backgroundColor: 'transparent' }}
            >
                Your apps
            </ContainerTitle>
            <AppCardsContainer
                id={'your-apps'}
                isDarkMode={isDarkMode}
                style={{ color: '#fff', backgroundColor: 'transparent' }}
            >
                <List
                    grid={{ gutter: 24, xs: 1, sm: 1, md: 2, lg: 3, xl: 4, xxl: 4 }}
                    size="large"
                    dataSource={products || []}
                    renderItem={(item) => {
                        const productCode = item.productCode ?? 'DEFAULT';
                        const logoUrl = logos[productCode] || logos.DEFAULT;
                        return (
                            <ProductListItem>
                                <Skeleton loading={isLoading}>
                                    <ProductCard
                                        title={item.productName || ''}
                                        extra={
                                            <StarFilled
                                                onClick={() =>
                                                    handleFavorite(item.productId, !item.isFavorite)
                                                }
                                                style={item.isFavorite ? { color: '#E7336D' } : {}}
                                            />
                                        }
                                        logoUrl={logoUrl}
                                        isDarkMode={isDarkMode}
                                        onClick={() => handleProductClick(item)}
                                    >
                                        {item.description}
                                    </ProductCard>
                                </Skeleton>
                            </ProductListItem>
                        );
                    }}
                />
            </AppCardsContainer>
        </GlassmorphismContainer>
    );
};

export default YourApps;
