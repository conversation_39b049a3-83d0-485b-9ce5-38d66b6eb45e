import React, { useEffect, useState } from 'react';
import { Menu, MenuProps } from 'antd';
import {
    FileAddOutlined,
    HomeOutlined,
    LogoutOutlined,
    ProductOutlined,
    SafetyOutlined,
} from '@ant-design/icons';
import { BottomMenu, CustomSider } from './Sidebar.styles';
import { useAuth } from '../../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../../context/ThemeContext';
import { useOktaAuth } from '@okta/okta-react';
import { useProducts } from '../../../hooks/useProducts.js';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
    label: React.ReactNode,
    key: React.Key,
    icon?: React.ReactNode,
    children?: MenuItem[],
    onClick?: () => void,
): MenuItem {
    return {
        key,
        icon,
        label,
        children,
        onClick,
    } as MenuItem;
}

interface SideBarProps {
    collapsed: boolean;
}

const Sidebar: React.FC<SideBarProps> = ({ collapsed }) => {
    const { logout } = useAuth();
    const navigate = useNavigate();
    const { isDarkMode } = useTheme();
    const [openKeys, setOpenKeys] = useState<string[]>([]);
    const [selectedKeys, setSelectedKeys] = useState<string[]>(['1']);

    const { authState } = useOktaAuth() || {};
    const isAuthenticated = !!(authState?.isAuthenticated && authState.accessToken);

    const { products, handleProductClick } = useProducts({ isAuthenticated });

    const handleLogout = () => {
        logout();
        navigate('/login');
    };

    const menuItems: MenuItem[] = [
        getItem('Home', '1', <HomeOutlined />, undefined, () => navigate('/')),
        getItem(
            'Product',
            '2',
            <ProductOutlined />,
            products?.map((product) =>
                getItem(
                    product.productName,
                    `product-${product.productId}`,
                    undefined,
                    undefined,
                    () => handleProductClick(product),
                ),
            ),
        ),
        getItem('Administration', '3', <SafetyOutlined />, [
            getItem('Tenant Management', '31', undefined, undefined, () =>
                navigate('/admin/tenant'),
            ),
        ]),
        // getItem('Favourites', '3', <StarOutlined />),
        getItem('Release Notes', '4', <FileAddOutlined />),
    ];

    const onMenuSelect = ({ key, keyPath }: { key: string; keyPath: string[] }) => {
        const isSubMenuItem = keyPath.length > 1;
        setSelectedKeys([key]);

        if (isSubMenuItem) {
            const parentKey = keyPath[1];
            setOpenKeys([parentKey]);
        } else {
            setOpenKeys([]);
        }
    };

    const onOpenChange = (keys: string[]) => {
        const lastKey = keys[keys.length - 1];
        setOpenKeys([lastKey]);
    };

    useEffect(() => {
        if (!collapsed && selectedKeys.length > 0) {
            const parentKey = selectedKeys[0].split('-')[0];
            setOpenKeys([parentKey]);
        }
    }, [collapsed, selectedKeys]);

    return (
        <CustomSider
            collapsible
            collapsed={collapsed}
            trigger={null}
            width={296}
            collapsedWidth={72}
            isDarkMode={isDarkMode}
        >
            <Menu
                mode="inline"
                defaultSelectedKeys={['1']}
                items={menuItems}
                openKeys={openKeys}
                selectedKeys={selectedKeys}
                onOpenChange={onOpenChange}
                onSelect={onMenuSelect}
            />
            <BottomMenu mode="inline" selectable={false} theme={'dark'}>
                <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
                    Logout
                </Menu.Item>
            </BottomMenu>
        </CustomSider>
    );
};

export default Sidebar;
