import styled from 'styled-components';
import { Layout, Menu, MenuProps } from 'antd';
import React from 'react';

const shouldForwardProp = (prop: string) => !['isDarkMode'].includes(prop);

export const CustomSider = styled(Layout.Sider).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean; collapsed: boolean }>`
    && {
        background-color: ${({ isDarkMode }) => (isDarkMode ? '#b388eb' : '#8152a0')};
        display: flex;
        flex-direction: column;
        height: calc(100vh - 64px);
        top: 64px;

        .ant-layout-sider-children {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .ant-menu-submenu.ant-menu-submenu-inline.ant-menu-submenu-open.ant-menu-submenu-selected {
            .ant-menu-submenu-title {
                background-color: ${({ isDarkMode }) =>
                        isDarkMode ? '#b388eb' : '#8152a0'} !important;
                color: ${({ isDarkMode }) => (isDarkMode ? '#292b2c' : '#ffffff')} !important;
            }
        }

        .ant-menu-submenu.ant-menu-submenu-vertical.ant-menu-submenu-selected {
            .ant-menu-submenu-title {
                background-color: ${({ isDarkMode }) =>
                        isDarkMode ? '#f7f3fd' : '#f2eef5'} !important;
                color: ${({ isDarkMode }) => (isDarkMode ? '#8d67c0' : '#4e1f6d')} !important;
            }
        }

        .ant-menu-submenu.ant-menu-submenu-vertical.ant-menu-submenu-selected
        .ant-menu-submenu-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 4px;
            height: 100%;
            background-color: ${({ isDarkMode }) => (isDarkMode ? '#8d67c0' : '#4e1f6d')};
            display: ${({ collapsed }) => (collapsed ? 'block' : 'none')};
        }

        .ant-menu-submenu {
            border-radius: 0 !important;
        }

        .ant-menu-item-selected {
            background-color: #fff !important;
            width: ${({ collapsed }) => (collapsed ? '72px' : '100%')} !important;
            height: 60px !important;
            position: relative;
            border-radius: 0 !important;
            margin: 0 !important;
            padding-left: ${({ collapsed }) => (collapsed ? '24px' : '0')};
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 16px;

            span {
                font-size: 16px !important;
                transition: all 0.3s ease;
            }

            // Left indicator bar
            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 4px;
                height: 100%;
                background-color: ${({ isDarkMode }) => (isDarkMode ? '#8d67c0' : '#4e1f6d')};
                display: ${({ collapsed }) => (collapsed ? 'block' : 'none')};
            }

            .anticon {
                padding-top: 0 !important;
                transition: padding-top 0.3s ease;
                display: grid;

                svg {
                    width: 24px;
                    height: 24px;
                    transition: all 0.3s ease;
                }
            }
        }

        .ant-menu-item:not(.ant-menu-item-selected):hover,
        .ant-menu-submenu-title:hover {
            background-color: ${({ isDarkMode }) =>
                    isDarkMode ? '#f7f3fd' : '#f2eef5'} !important;
            width: ${({ collapsed }) => (collapsed ? '72px' : '100%')} !important;
            height: 60px !important;
            position: relative;
            border-radius: 0 !important;
            margin: 0 !important;
            padding-left: ${({ collapsed }) => (collapsed ? '24px' : '0')};
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 16px;

            span {
                font-size: 16px !important;
                transition: all 0.3s ease;
            }

            &::before {
                display: none;
            }

            .anticon {
                padding-top: 0 !important;
                transition: padding-top 0.3s ease;
                display: grid;

                svg {
                    width: 24px;
                    height: 24px;
                    transition: all 0.3s ease;
                }
            }
        }

        .ant-menu-submenu-title {
            display: flex;
            align-items: center;
            gap: 16px;
            margin: 0 !important;
            border-radius: 0 !important;
            position: relative;
            padding-left: ${({ collapsed }) => (collapsed ? '24px' : '16px')};
            width: 100% !important;
            height: 60px !important;
            transition: all 0.3s ease;

            span {
                font-size: 16px !important;
                transition: all 0.3s ease;
            }

            .anticon {
                padding-top: 0 !important;
                transition: padding-top 0.3s ease;
                display: grid;

                svg {
                    width: 24px;
                    height: 24px;
                    transition: all 0.3s ease;
                }
            }
        }

        .ant-menu-sub {
            .ant-menu-item-only-child {
                padding-left: 72px !important;
            }
        }

        // Submenu arrow rotation when open/closed
        .ant-menu-submenu-open .ant-menu-submenu-arrow {
            transform: rotate(90deg);
        }

        // SubMenu title styling (padding for icon and arrow)
        .ant-menu-submenu-title > .ant-menu-submenu-arrow {
            position: absolute;
            right: ${({ collapsed }) => (collapsed ? '24px' : '32px')};
            transition: transform 0.3s ease;
        }

        .ant-menu-item:not(.ant-menu-item-selected):not(:hover):not(:focus),
        .ant-menu-submenu-title:not(:hover):not(:focus) {
            background-color: ${({ isDarkMode }) =>
                    isDarkMode ? '#b388eb' : '#8152a0'} !important;
            color: ${({ isDarkMode }) => (isDarkMode ? '#292b2c' : '#ffffff')} !important;
            width: ${({ collapsed }) => (collapsed ? '72px' : '100%')} !important;
            height: 60px !important;
            border-radius: 0 !important;
            margin: 0 !important;
            padding-left: ${({ collapsed }) => (collapsed ? '24px' : '0')};
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 16px;

            span {
                font-size: 16px !important;
                transition: all 0.3s ease;
            }

            &::before {
                display: none;
            }

            .anticon {
                padding-top: 0 !important;
                transition: padding-top 0.3s ease;
                display: grid;

                svg {
                    width: 24px;
                    height: 24px;
                    transition: all 0.3s ease;
                }
            }
        }
    }
`;

export const BottomMenu: React.FC<MenuProps> = styled(Menu)`
    margin-top: auto; /* This will push the logout item to the bottom */
    border-top: 1px solid rgba(255, 255, 255, 0.1); /* Optional: Add a border to separate the logout item */
`;
