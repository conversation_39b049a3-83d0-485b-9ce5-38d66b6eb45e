import React from 'react';
import { MoonOutlined, SunOutlined, MenuOutlined } from '@ant-design/icons';
import { CustomHeader, LogoImage, ThemeToggle, MenuButton } from './Header.styles';
import logoWhite from '../../../assets/images/ah-logo-w.png';
import logoBlack from '../../../assets/images/ah-logo-b.png';
import { Switch } from 'antd';
import ProfileButtonComponent from '../Profile/ProfileButton';
import { useTheme } from '../../../context/ThemeContext';

interface HeaderProps {
    toggleCollapsed: () => void;
}

const Header: React.FC<HeaderProps> = ({ toggleCollapsed }) => {
    const { isDarkMode, toggleTheme } = useTheme();
    return (
        <CustomHeader id={'custom-header'} isDarkMode={isDarkMode}>
            <MenuButton icon={<MenuOutlined />} onClick={toggleCollapsed} isDarkMode={isDarkMode} />
            <LogoImage src={isDarkMode ? logoBlack : logoWhite} alt="Amplify Health Logo" />
            {/*<SearchBar placeholder="Search here..." prefix={<SearchOutlined />} />*/}
            <ThemeToggle>
                <Switch
                    checked={isDarkMode}
                    onChange={toggleTheme}
                    checkedChildren={<SunOutlined />}
                    unCheckedChildren={<MoonOutlined />}
                />
            </ThemeToggle>
            <ProfileButtonComponent />
        </CustomHeader>
    );
};

export default Header;
