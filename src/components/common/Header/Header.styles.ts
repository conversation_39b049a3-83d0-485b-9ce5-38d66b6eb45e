import styled from 'styled-components';
import { Layout, Input, Button } from 'antd';

const shouldForwardProp = (prop: string) => !['isDarkMode'].includes(prop);

export const CustomHeader = styled(Layout.Header).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
  background-color: ${({ isDarkMode }) => (isDarkMode ? '#B388EB' : '#8152a0')};
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 64px;
  z-index: 1000;
  position: fixed;
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1);
`;

export const MenuButton = styled(Button).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    background: transparent !important;
    color: ${({ isDarkMode }) => (isDarkMode ? '#292B2C' : '#ffffff')};
    border: none;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 4px;
`;

export const SearchBar = styled(Input)`
    max-width: 524px;
    width: 524px;
    margin-left: auto;
    padding-left: 16px;
    padding-right: 16px;
    border: none;

    .ant-input-prefix {
        color: #9aaaab;
        margin-right: 8px;
    }

    ::placeholder {
        color: #9aaaab;
    }
`;

export const LogoImage = styled.img`
  max-height: 100%;
  margin-right: 20px; /* Space between logo and search bar */
  margin-left: 16px;
  width: 152px;
  height: 32px;
`;

export const ThemeToggle = styled.div`
  margin-right: 16px;
  //margin-left: 16px;
  margin-left: auto;
`;
