import React, { useEffect, useState } from 'react';
import { Button, Result } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';

interface ErrorPageProps {
    title?: string;
    description?: string;
    countdown?: number;
}

const AuthenticationErrorPage: React.FC<ErrorPageProps> = ({
    title = 'Oops! Something went wrong',
    description = 'An unexpected error occurred. Please try again later.',
    countdown = 5,
}) => {
    const navigate = useNavigate();
    const location = useLocation();
    const [timeLeft, setTimeLeft] = useState<number>(countdown);

    const errorDescription = location.state?.error || description;

    useEffect(() => {
        const timer = setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    clearInterval(timer);
                    handleRedirectNow();
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [navigate]);

    const handleRedirectNow = () => {
        sessionStorage.removeItem('callbackHandled');
        sessionStorage.setItem('redirectedFromAuthenticationErrorPage', 'true');
        navigate('/login');
    };

    return (
        <Result
            status="error"
            title={title}
            subTitle={
                <>
                    <p>{errorDescription}</p>
                    <p>
                        Redirecting to login in <strong>{timeLeft}</strong> seconds...
                    </p>
                </>
            }
            extra={[
                <Button type="primary" key="login" onClick={handleRedirectNow}>
                    Go to Login Now
                </Button>,
            ]}
        />
    );
};

export default AuthenticationErrorPage;
