import styled from 'styled-components';
import { Card } from 'antd';

const shouldForwardProp = (prop: string) => !['isDarkMode', 'isVisible'].includes(prop);

export const ProductCardContainer = styled(Card).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    &:hover {
        transform: translateY(-5px); /* Slightly lift the card */
        box-shadow: ${({ isDarkMode }) =>
            isDarkMode
                ? '0 4px 30px rgba(232, 85, 85, 0.3)'
                : '0 4px 30px rgb(207, 41, 125, 0.9)'}; /* Increase shadow on hover */
    }
`;

export const CoverImage = styled.div`
    img {
        width: 100%;
        height: auto;
        border-radius: 8px 8px 0 0;
    }
`;

export const TitleRow = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px; /* Space between the image and the title row */
`;

// Wrapper for the logo with a fixed size
export const LogoWrapper = styled.div`
    width: 100%;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background: #eaeaea;
    border-radius: 4px;
`;

// Style for the product logo
export const ProductLogo = styled.img`
    width: 40%;
    height: 100%;
    object-fit: contain; // Ensures that the logo retains its aspect ratio within the container
`;
