import React from 'react';
import { Card } from 'antd';
import { ProductCardContainer, TitleRow, LogoWrapper, ProductLogo } from './ProductCard.styles';

interface ProductCardProps {
    title: string;
    extra: React.ReactNode;
    logoUrl?: string;
    children: React.ReactNode;
    isDarkMode: boolean;
    onClick?: () => void;
}

const ProductCard: React.FC<ProductCardProps> = ({
    title,
    // extra,
    logoUrl,
    children,
    isDarkMode,
    onClick,
}) => {
    return (
        <ProductCardContainer isDarkMode={isDarkMode} onClick={onClick}>
            <LogoWrapper>{logoUrl && <ProductLogo src={logoUrl} alt={title} />}</LogoWrapper>
            <TitleRow>
                <Card.Meta title={title} />
                {/*{extra && <div style={{ marginTop: 8 }}>{extra}</div>}*/}
            </TitleRow>

            <div style={{ marginTop: 16 }}>{children}</div>
        </ProductCardContainer>
    );
};

export default ProductCard;
