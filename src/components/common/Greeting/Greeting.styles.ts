import styled from 'styled-components';
import { Typography } from 'antd';
const { Title, Text } = Typography;

const shouldForwardProp = (prop: string) => !['isDarkMode', 'isVisible'].includes(prop);

export const GreetingContainer = styled.div.withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    background: ${({ isDarkMode }) =>
        isDarkMode
            ? 'linear-gradient(139.63deg, #9158c1 21.61%, #f94a8c 48.55%, #ff744a 83.44%)'
            : 'linear-gradient(91.15deg, #8152A0 -9.9%, #F8116A 46.47%, #FF5A22 106.33%)'};
    padding: 30px;
    border-radius: 0;
    color: #ffffff; /* White text color */
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 312px; /* Adjust height to match the visual */
    position: relative; /* Needed for the overlay image */
    overflow: hidden; /* Ensure the image does not overflow */
`;

export const TextContainer = styled.div`
    display: flex;
    flex-direction: column;
    margin-top: -200px;
`;

export const FancyTitle = styled(Title).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    && {
        font-size: 2.5rem;
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 16px;
    }
`;

export const FancyDate = styled(Text).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    font-size: 1.2rem;
    color: #ffffff;
`;

export const ImageOverlay = styled.img`
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    height: 100%; /* Ensure it covers the full height */
    width: auto; /* Maintain aspect ratio */
`;
