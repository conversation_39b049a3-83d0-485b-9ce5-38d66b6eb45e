import React from 'react';
import {
    <PERSON>cy<PERSON>ate,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>reetingContainer,
    ImageOverlay,
    TextContainer,
} from './Greeting.styles';
import Set3Image from '../../../assets/images/set3.png';
import { jwtDecode } from 'jwt-decode';

interface GreetingProps {
    isDarkMode: boolean;
}

interface DecodedToken {
    name?: string;
}

const Greeting: React.FC<GreetingProps> = ({ isDarkMode }) => {
    // Get the current date
    const currentDate = new Date().toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    });

    // Get the current hour
    const currentHour = new Date().getHours();

    // Determine the appropriate greeting based on the time of day
    const getGreeting = () => {
        if (currentHour < 12) {
            return 'Good Morning';
        } else if (currentHour < 18 && currentHour >= 12) {
            return 'Good Afternoon';
        } else {
            return 'Good Evening';
        }
    };

    const getNameFromToken = () => {
        const oktaTokenStorage = sessionStorage.getItem('okta-token-storage');
        if (oktaTokenStorage) {
            try {
                const tokenData = JSON.parse(oktaTokenStorage);
                const accessToken = tokenData?.accessToken?.accessToken;

                if (accessToken) {
                    const decodedToken: DecodedToken = jwtDecode(accessToken);
                    return decodedToken.name || 'Guest';
                }
            } catch (error) {
                console.error('Error decoding access token: ', error);
            }
        }
        return 'Guest';
    };

    const userName = getNameFromToken();

    return (
        <GreetingContainer isDarkMode={isDarkMode}>
            <TextContainer>
                <FancyTitle level={2} isDarkMode={isDarkMode}>
                    {getGreeting()}, {userName}!
                </FancyTitle>
                <FancyDate isDarkMode={isDarkMode}>{currentDate}</FancyDate>
            </TextContainer>
            <ImageOverlay src={Set3Image} />
        </GreetingContainer>
    );
};

export default Greeting;
