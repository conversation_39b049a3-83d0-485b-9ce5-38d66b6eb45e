export type ProductConfig = {
    productId: number | null;
    productCode: string;
    configType: string;
    configValue: string;
    configValueFormatType: string;
    regex: string;
    productConfigStatus: string;
};

export type Product = {
    productId?: number;
    productCode?: string;
    productName?: string;
    description?: string;
    productStatus?: string;
    productConfigs?: ProductConfig[];
    isFavorite?: boolean;
};
