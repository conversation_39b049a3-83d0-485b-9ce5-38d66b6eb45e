import React from 'react';
import MainLayout from './components/layout/MainLayout';
import './App.css';
import { Route, Routes } from 'react-router-dom';
import PrivateRoute from './components/route/PrivateRoute';
import LandingPage from './pages/LandingPage';
import TenantManagementPage from './pages/TenantManagementPage';

const App: React.FC = () => {
    return (
        <Routes>
            <Route path="/" element={<PrivateRoute element={<MainLayout />} />}>
                <Route index element={<LandingPage />} />
                <Route path="admin/tenant" element={<TenantManagementPage />} />
            </Route>
        </Routes>
    );
};

export default App;
