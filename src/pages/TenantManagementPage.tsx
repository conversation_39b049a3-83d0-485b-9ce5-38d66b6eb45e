import React, { useEffect, useState } from 'react';
import { Breadcrumb, Table, Space, Input } from 'antd';
import { EditOutlined, DeleteOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import {
    PageContainer,
    HeaderSection,
    Title,
    DateTimeInfo,
    Toolbar,
    CreateButton,
    TenantCode,
    ActionIcon,
    StyledSearchInput,
    ToolbarLeft,
    ContentWrapper,
    BreadcrumbCurrent,
    PillTag,
} from './TenantManagementPage.style';

const tenantData = [
    {
        key: '1',
        organization: 'AIA Philippines',
        country: 'Philippines',
        code: 'AIA_PH',
        status: 'Active',
    },
    {
        key: '2',
        organization: 'AIA Hongkong',
        country: 'Hongkong',
        code: 'AIA_HK',
        status: 'Active',
    },
    {
        key: '3',
        organization: 'AIA Singapore',
        country: 'Singapore',
        code: 'AIA_CN',
        status: 'Active',
    },
    { key: '4', organization: 'AIA India', country: 'India', code: 'AIA_IN', status: 'Inactive' },
    {
        key: '5',
        organization: 'AIA Indonesia',
        country: 'Indonesia',
        code: 'AIA_ID',
        status: 'Active',
    },
    {
        key: '6',
        organization: 'AIA Malaysia',
        country: 'Malaysia',
        code: 'AIA_MY',
        status: 'Inactive',
    },
    {
        key: '7',
        organization: 'AIA Myanmar',
        country: 'Myanmar',
        code: 'AIA_MM',
        status: 'Inactive',
    },
    {
        key: '8',
        organization: 'AIA South Korea',
        country: 'South Korea',
        code: 'AIA_KR',
        status: 'Active',
    },
    { key: '9', organization: 'AIA AB', country: 'AB', code: 'AIA_AB', status: 'Active' },
    { key: '10', organization: 'AIA CD', country: 'CD', code: 'AIA_CD', status: 'Inactive' },
];

const TenantManagementPage: React.FC = () => {
    const [currentTime, setCurrentTime] = useState(dayjs().format('MMMM D, YYYY | HH:mm:ss'));

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(dayjs().format('MMMM D, YYYY | HH:mm:ss'));
        }, 1000);
        return () => clearInterval(interval);
    }, []);

    const columns = [
        {
            title: 'Organization Name',
            dataIndex: 'organization',
            key: 'organization',
        },
        {
            title: 'Country',
            dataIndex: 'country',
            key: 'country',
        },
        {
            title: 'Tenant Code',
            dataIndex: 'code',
            key: 'code',
            render: (text: string) => <TenantCode>{text}</TenantCode>,
        },
        {
            title: 'Tenant Status',
            dataIndex: 'status',
            key: 'status',
            render: (status: string) => (
                <PillTag
                    color={status === 'Active' ? '#D5E4D6' : '#F8D4D4'}
                    style={{ color: status === 'Active' ? '#004700' : '#921B1B' }}
                >
                    {status}
                </PillTag>
            ),
        },
        {
            title: 'Actions',
            key: 'actions',
            render: () => (
                <Space>
                    <ActionIcon>
                        <EditOutlined />
                    </ActionIcon>
                    <ActionIcon>
                        <DeleteOutlined />
                    </ActionIcon>
                </Space>
            ),
        },
    ];

    return (
        <PageContainer>
            <Breadcrumb style={{ marginBottom: 16 }}>
                <Breadcrumb.Item>Home</Breadcrumb.Item>
                <Breadcrumb.Item>Administration</Breadcrumb.Item>
                <Breadcrumb.Item>
                    <BreadcrumbCurrent>
                        <span style={{ color: '#FF4081' }}>Tenant Management</span>
                    </BreadcrumbCurrent>
                </Breadcrumb.Item>
            </Breadcrumb>

            <HeaderSection>
                <Title>View Tenants List</Title>
                <DateTimeInfo>
                    <div>{currentTime}</div>
                    <div>Last update: Dec 2023</div>
                </DateTimeInfo>
            </HeaderSection>

            <ContentWrapper>
                <Toolbar>
                    <ToolbarLeft>
                        <StyledSearchInput>
                            <Input
                                placeholder="Search here..."
                                prefix={<SearchOutlined style={{ color: 'rgba(0,0,0,.45)' }} />}
                            />
                        </StyledSearchInput>
                        <CreateButton>
                            <PlusOutlined /> Create New Tenant
                        </CreateButton>
                    </ToolbarLeft>
                </Toolbar>

                <Table
                    columns={columns}
                    dataSource={tenantData}
                    pagination={{ pageSize: 10 }}
                    bordered
                />
            </ContentWrapper>
        </PageContainer>
    );
};

export default TenantManagementPage;
