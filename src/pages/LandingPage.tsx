import React from 'react';
import { useLoading } from '../context/LoadingContext';
import Greeting from '../components/common/Greeting/Greeting';
import { useTheme } from '../context/ThemeContext';
import YourApps from '../components/common/Dashboard/YourApps';
import { GreetingContent } from './LandingPage.style.js';

const LandingPage: React.FC = () => {
    const { isDarkMode } = useTheme();
    const { setLoading, setLoadingText } = useLoading();
    return (
        <>
            <GreetingContent isDarkMode={isDarkMode}>
                <Greeting isDarkMode={isDarkMode} />
            </GreetingContent>
            <YourApps setLoading={setLoading} setLoadingText={setLoadingText} />
        </>
    );
};

export default LandingPage;
