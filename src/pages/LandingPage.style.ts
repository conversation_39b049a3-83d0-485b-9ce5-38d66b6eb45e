import styled from 'styled-components';
import { Layout } from 'antd';

const shouldForwardProp = (prop: string) => !['isDarkMode', 'isVisible'].includes(prop);

export const GreetingContent = styled(Layout.Content).withConfig({
    shouldForwardProp,
})<{ isDarkMode: boolean }>`
    flex: none;
    height: 312px;
    width: 100%;
    transition: width 0.2s ease-in-out;

    h3 {
        color: ${({ isDarkMode }) => (isDarkMode ? '#ffffff' : '#000000')};
    }
`;
