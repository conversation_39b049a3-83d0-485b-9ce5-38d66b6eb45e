import styled from 'styled-components';
import { Tag } from 'antd';

export const PageContainer = styled.div`
    padding: 24px 40px;
    background-color: white;
`;

export const HeaderSection = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
`;

export const Title = styled.h2`
    margin: 0;
`;

export const DateTimeInfo = styled.div`
    text-align: right;
    font-size: 14px;

    div:last-child {
        font-size: 12px;
        color: gray;
    }
`;

export const Toolbar = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
`;

export const ToolbarLeft = styled.div`
    display: flex;
    align-items: center;
    flex: 1;
    gap: 12px;
`;

export const CreateButton = styled.button`
    background-color: #ff4081;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 6px 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;

    &:hover {
        background-color: #e7336d;
    }
`;

export const TenantCode = styled.span`
    color: #ff4081;
    font-weight: 500;
`;

export const ActionIcon = styled.span`
    color: #ff4081;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
`;

export const StyledSearchInput = styled.div`
    flex: 1;

    .ant-input-affix-wrapper {
        width: 100%;
        border-color: #d9d9d9;
        border-radius: 8px;
        color: rgba(0, 0, 0, 0.45);

        input::placeholder {
            color: rgba(0, 0, 0, 0.45);
        }
    }
`;

export const ContentWrapper = styled.div`
    padding: 0;

    .ant-table-wrapper {
        padding: 0;
        border-radius: 8px;
        overflow: hidden;
        border: 1.2px solid #f0f0f0;
    }

    .ant-table td,
    .ant-table th {
        border-right: none !important;
        border-width: 1.2px;
    }

    .ant-table th {
        font-weight: 600;
        background-color: #eaeaea;
    }

    .ant-table-content > table {
        border-collapse: collapse !important;
    }

    .ant-pagination {
        margin: 0;
    }

    .ant-table-pagination {
        padding: 0;
        margin-top: 0;
        border-top: none;
    }
`;

export const BreadcrumbCurrent = styled.span`
    font-weight: 600;
`;

export const PillTag = styled(Tag)`
    border-radius: 999px;
    border: none;
`;