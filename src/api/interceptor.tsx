import { useAuth } from '../context/AuthContext.js';
import { useEffect } from 'react';
import productPortalApi from './ppApi.js';
import uamApi from './uamApi.js';
import { AxiosInstance } from 'axios';

export const ApiProvider = ({ children }: { children: React.ReactNode }) => {
    const { oktaAuth } = useAuth();
    useEffect(() => {
        const setupInterceptors = (apiInstance: AxiosInstance) => {
            const requestInterceptor = apiInstance.interceptors.request.use(
                async (config) => {
                    const accessToken = await oktaAuth?.getAccessToken();
                    if (accessToken) {
                        config.headers['Authorization'] = `Bearer ${accessToken}`;
                    }

                    return config;
                },
                (error) => {
                    return Promise.reject(error);
                },
            );

            return () => {
                apiInstance.interceptors.request.eject(requestInterceptor);
            };
        };
        setupInterceptors(productPortalApi);
        setupInterceptors(uamApi);
    }, [oktaAuth]);

    return <>{children}</>;
};
