import { ApiResponse } from '../../models/apiResponse';
import { Product } from '../../models/product';
import productPortalApi from '../ppApi';

export const fetchProducts = async (): Promise<Product[]> => {
    const { data } = await productPortalApi.get<ApiResponse<Product[]>>('/user-product');
    return data?.data || [];
};

export const fetchFavoriteProducts = async (): Promise<Product[]> => {
    const { data } = await productPortalApi.get<ApiResponse<Product[]>>('/user-product/favorite');
    return data?.data || [];
};

export const updateFavoriteProducts = async (product: Product): Promise<Product> => {
    const { data } = await productPortalApi.put<ApiResponse<Product>>(
        '/user-product/favorite',
        product,
    );
    return data?.data || product;
};
