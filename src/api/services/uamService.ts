import uamApi from '../uamApi.js';
import { ApiResponse } from '../../models/apiResponse.js';
import { DynamicOktaConfig } from '../../models/dynamicOktaConfig.js';
import { DynamicOktaConfigResponse } from '../../models/dynamicOktaConfigResponse.js';

export const getRedirectUrl = async (
    appId: string,
    redirectUrl: string | null,
): Promise<string> => {
    if (!isValidRedirectUrl(redirectUrl)) {
        throw new Error('Invalid redirect URL');
    }
    const { data } = await uamApi.post<ApiResponse<string>>(`/user/generate-state-token/${appId}`, {
        redirectUrl,
    });
    return data?.data || '';
};

const isValidRedirectUrl = (redirectUrl: string | null) => {
    if (!redirectUrl) {
        return true;
    }
    try {
        const parsedRedirectUrl = new URL(redirectUrl);
        return parsedRedirectUrl.protocol === 'http:' || parsedRedirectUrl.protocol === 'https:';
    } catch (e) {
        return false;
    }
};

export const getOktaAuthConfig = async (email: string): Promise<DynamicOktaConfig> => {
    try {
        const { data } = await uamApi.get<ApiResponse<DynamicOktaConfigResponse>>('/public/sso-url', {
            params: { email },
        });
        if (!data?.data) {
            throw new Error('No valid data returned');
        }
        return {
            clientId: data.data.clientId,
            issuer: data.data.ssoUrl,
        };
    } catch (e) {
        throw new Error(e instanceof Error ? e.message : 'Failed to fetch dynamic okta auth config');
    }

};

export const revokeAccessToken = async (): Promise<string> => {
    const { data } = await uamApi.post<ApiResponse<string>>('/user/revoke-access-token');
    return data?.data || '';
};
