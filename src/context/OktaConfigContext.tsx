import React, { createContext, ReactNode, useContext, useState } from 'react';
import oktaStaticConfig from '../config/OktaConfig';

interface OktaConfig {
    clientId: string;
    issuer: string;
}

interface OktaConfigContextType {
    oktaConfig: OktaConfig | null;
    setOktaConfig: (config: OktaConfig) => void;
}

const OktaConfigContext = createContext<OktaConfigContextType | undefined>(undefined);

export const OktaConfigProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [oktaConfig, setOktaConfig] = useState<OktaConfig | null>(null);

    const setMergedOktaConfig = (dynamicConfig: OktaConfig) => {
        const mergedConfig = {
            ...oktaStaticConfig,
            ...dynamicConfig,
        };
        setOktaConfig(mergedConfig);
    };

    return (
        <OktaConfigContext.Provider value={{ oktaConfig, setOktaConfig: setMergedOktaConfig }}>
            {children}
        </OktaConfigContext.Provider>
    );
};

export const useOktaConfig = () => {
    const context = useContext(OktaConfigContext);
    if (!context) {
        throw new Error('useOktaConfig must be used within an OktaConfigProvider');
    }
    return context;
};
