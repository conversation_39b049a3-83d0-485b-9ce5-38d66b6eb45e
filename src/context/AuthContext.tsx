import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { config } from '../config/Config';
import { useNavigate } from 'react-router-dom';
import OktaAuth, { AccessToken } from '@okta/okta-auth-js';
import { notification } from 'antd';
import { revokeAccessToken } from '../api/services/uamService.js';

interface AuthContextType {
    oktaAuth: OktaAuth | null;
    isAuthenticated: boolean | undefined;
    login: (email?: string) => void;
    logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode; oktaAuth: OktaAuth | null }> = ({
                                                                                               children,
                                                                                               oktaAuth,
                                                                                           }) => {
    const [loading, setLoading] = useState(true);
    const [isAuthenticated, setIsAuthenticated] = useState<boolean | undefined>(undefined);
    const [, setCountDown] = useState<number | null>(null);
    const navigate = useNavigate();

    useEffect(() => {
        const checkAuth = async () => {
            if (config.useOktaAuth && oktaAuth) {
                try {
                    const isAuth = await oktaAuth.isAuthenticated();
                    console.log('Is Authenticated from oktaAuth:', isAuth);

                    if (!isAuth) {
                        console.log('Pathname: ', window.location.pathname);
                        if (window.location.pathname === '/callback') {
                            setIsAuthenticated(false);
                            return;
                        }

                        if (sessionStorage.getItem('callbackHandled') === 'true') {
                            setIsAuthenticated(false);
                            return;
                        }

                        if (
                            sessionStorage.getItem('redirectedFromAuthenticationErrorPage') ===
                            'true'
                        ) {
                            setIsAuthenticated(false);
                            sessionStorage.removeItem('redirectedFromAuthenticationErrorPage');
                            return;
                        }

                        login();
                    } else {
                        setIsAuthenticated(true);
                        startTokenExpirationWatcher();
                    }
                } catch (error) {
                    console.error('Error checking authentication:', error);
                    navigate('/login');
                }
            } else {
                setIsAuthenticated(false);
            }
        };
        checkAuth();
    }, [oktaAuth]);

    useEffect(() => {
        if (isAuthenticated !== undefined) {
            setLoading(false);
        }
    }, [isAuthenticated]);

    const startTokenExpirationWatcher = async () => {
        console.log('Starting TokenExpiration Watcher');
        try {
            const token = oktaAuth
                ? await oktaAuth.tokenManager.get('accessToken')
                : { expiresAt: (Date.now() + 1800000) / 1000 };
            if (!token || !token.expiresAt) {
                return;
            }

            const expirationTime = token.expiresAt * 1000;

            const countDownInterval = setInterval(() => {
                const currentTime = Date.now();
                const timeRemaining = expirationTime - currentTime;

                if (timeRemaining <= 10000 && timeRemaining > 0) {
                    const secondLeft = Math.ceil(timeRemaining / 1000);
                    setCountDown(secondLeft);

                    notification.open({
                        message: 'Auto-logout Warning',
                        description: `Logout in ${secondLeft} seconds.`,
                        duration: 0,
                    });
                }

                if (timeRemaining <= 0) {
                    clearInterval(countDownInterval);
                    notification.destroy();
                    logout();
                    navigate('/login');
                }
            }, 1000);
        } catch (e) {
            console.error('Error retrieving access token:', e);
        }
    };

    const login = (email?: string) => {
        if (config.useOktaAuth && oktaAuth) {
            try {
                // Trigger OKTA's login process with the dynamic config
                oktaAuth?.signInWithRedirect({
                    originalUri: '/', // Redirect after successful login
                    ...(email && { loginHint: email }), // Pass email if provided
                });
                console.log('Redirection to Okta login page initiated:', oktaAuth);
            } catch (error) {
                console.log('Error during Okta signInWithRedirect:', error);
            }
        } else {
            setIsAuthenticated(true);
            navigate('/');
            startTokenExpirationWatcher();
            console.log('OKTA login is disabled. User is manually authenticated.');
        }
    };

    const logout = async () => {
        if (config.useOktaAuth && oktaAuth) {
            try {
                const accessToken = (await oktaAuth.tokenManager.get('accessToken')) as
                    | AccessToken
                    | undefined;

                if (accessToken?.accessToken) {
                    await revokeAccessToken();
                }

                oktaAuth.tokenManager.clear();

                sessionStorage.removeItem('callbackHandled');

                setIsAuthenticated(false);
            } catch (e) {
                console.error('Error logging out:', e);
            }
        } else {
            setIsAuthenticated(false);
            console.log('OKTA login is disabled. User is manually logged out.');
        }
    };

    const isCallBackRoute = location.pathname === '/callback';

    return (
        <AuthContext.Provider value={{ oktaAuth, isAuthenticated, login, logout }}>
            {(!loading || isCallBackRoute) && children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within a AuthProvider');
    }
    return context;
};
