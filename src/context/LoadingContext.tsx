import React, { createContext, ReactNode, useContext, useState } from 'react';

interface LoadingContextType {
    loading: boolean;
    loadingText: string;
    setLoading: (value: boolean) => void;
    setLoadingText: (value: string) => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const LoadingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [loading, setLoading] = useState(false);
    const [loadingText, setLoadingText] = useState('Loading...');

    return (
        <LoadingContext.Provider value={{ loading, loadingText, setLoading, setLoadingText }}>
            {children}
        </LoadingContext.Provider>
    );
};

export const useLoading = () => {
    const context = useContext(LoadingContext);
    if (!context) {
        throw new Error('useLoading must be used within a LoadingProvider');
    }
    return context;
};
